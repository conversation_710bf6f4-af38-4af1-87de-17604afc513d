from datetime import datetime, timezone
from select import select

from gc_dentist_shared.central_models import MasterPricingStorage, TenantClinic, TenantExtraStorage
from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from db.db_connection import TenantDatabase
from sqlalchemy import func, update
from sqlalchemy.ext.asyncio import AsyncSession
from gc_dentist_shared.tenant_models import TenantConfiguration

from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.constants import StorageRedis
from gc_dentist_shared.core.enums.pricing_enum import TenantExtraStorageStatus
from gc_dentist_shared.core.logger.config import log


class PricingLambdaService:
    def __init__(self, central_db_session: AsyncSession, redis_cli: RedisCli = None):
        self.central_db_session = central_db_session
        self.redis_cli = redis_cli

    # region Private Common Methods

    # endregion

    # region Lambda clean up pricing storage expired
    async def clean_up_pricing_storage_expired(self):
        log.info(" START Clean up pricing storage expired")

        async with self.central_db_session.begin():
            # Step 1 Get all tenant extra storage expired
            tenant_extra_storages = await self._get_tenant_extra_storage_expired(
                self.central_db_session
            )

            if not tenant_extra_storages:
                log.info("No tenant extra storage expired")

                log.info(" END Clean up pricing storage expired")
                return

            # Step 2 Update status to expired
            tenant_extra_storage_ids = [item["ids"] for item in tenant_extra_storages]
            await self._update_tenant_extra_storage_status(
                self.central_db_session,
                tenant_extra_storage_ids,
                TenantExtraStorageStatus.EXPIRED.status_id,
            )

            # Step 3 Update total extra storage of tenant
            # await self._sync_total_pricing_storage_expired_to_tenant(
            #     tenant_extra_storages
            # )

        log.info(" END Clean up pricing storage expired")

    async def _get_tenant_extra_storage_expired(self, db_session: AsyncSession):
        current_time = datetime.now(timezone.utc)
        try:
            query = (
                select(
                    TenantExtraStorage.tenant_uuid,
                    TenantClinic.db_name,
                    func.sum(MasterPricingStorage.storage).label("total_storage"),
                    func.array_agg(TenantExtraStorage.id).label("ids"),
                )
                .join(
                    MasterPricingStorage,
                    MasterPricingStorage.storage_key_id ==
                    TenantExtraStorage.storage_key_id
                )
                .join(
                    TenantClinic, TenantClinic.tenant_uuid == TenantExtraStorage.tenant_uuid
                )
                .where(
                    TenantExtraStorage.expired_at < current_time,
                    # TenantExtraStorage.expired_at < func.now(),
                    # TenantExtraStorage.status == TenantExtraStorageStatus.ACTIVE.status_id,
                )
                .group_by(TenantExtraStorage.tenant_uuid, TenantClinic.db_name)
            )
            result = await db_session.execute(query)
            return result.mappings().all()
        except Exception as e:
            log.error(f"❌ Error getting tenant extra storage expired: {str(e)}")
            return []

    async def _update_tenant_extra_storage_status(
        self, db_session: AsyncSession, tenant_extra_storages: list, status: int
    ):
        query = (
            update(TenantExtraStorage)
            .where(TenantExtraStorage.id.in_(tenant_extra_storages))
            .values(status=status)
        )
        await db_session.execute(query)

    async def _sync_total_pricing_storage_expired_to_tenant(
        self, tenant_extra_storages
    ):
        log.info(" START Sync total extra storage expired to tenant")

        for tenant in tenant_extra_storages:
            log.info(
                f" START Sync total extra storage expired to tenant {tenant.tenant_uuid}"
            )
            token = set_current_db_name(tenant.tenant_uuid)
            try:
                tenant_db_session = await TenantDatabase.get_instance_tenant_db()
                async with tenant_db_session.begin():
                    await self._update_total_extra_storage_of_tenant(
                        tenant_db_session, tenant
                    )
                    await self._update_total_extra_storage_to_redis(tenant)
            except Exception as e:
                log.error(f"❌ Error setting current database name: {e}")
                continue
            finally:
                reset_current_db_name(token)
                log.info(
                    f" END Sync total extra storage expired to tenant {tenant.tenant_uuid}"
                )

        log.info(" END Sync total extra storage expired to tenant")

    async def _update_total_extra_storage_of_tenant(
        self, db_session: AsyncSession, tenant: dict
    ):
        query = select(TenantConfiguration).where(
            TenantConfiguration.tenant_uuid == tenant.tenant_uuid
        )
        result = await db_session.execute(query)
        tenant_config = result.scalar_one_or_none()
        if not tenant_config:
            log.error(f"❌ Tenant config not found for tenant {tenant.tenant_uuid}")
            return

        if tenant_config.extra_storage < tenant.total_extra_storage:
            log.error(
                f"❌ Tenant config extra storage is less than total extra storage for tenant {tenant.tenant_uuid}"
            )
            return

        tenant_config.extra_storage -= tenant.total_extra_storage
        await db_session.commit()

    async def _update_total_extra_storage_to_redis(self, tenant: dict):
        prefix = StorageRedis.STORAGE_LIMIT.value % tenant.tenant_uuid
        total_tenant_storage = tenant.total_extra_storage + tenant.default_storage
        is_success = await self.redis_cli.set(prefix, total_tenant_storage)
        if not is_success:
            log.error(
                f"❌ Error setting total extra storage to redis for tenant {tenant.tenant_uuid}"
            )

    # endregion
