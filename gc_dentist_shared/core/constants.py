from enum import StrEnum

TWILIO_SMS_CHANNEL = "sms"
TWILIO_STATUS_APPROVED = "approved"

FIELDS_ENCRYPTED = [
    "date_of_birth",
    "address_1",
    "address_2",
    "address_3",
    "phone",
    "email",
]

MAPPING_LANG_REGION = {
    "ja": "ja-JP",
    "en": "en-US",
}

X_TENANT_SLUG = "X-Tenant-Slug"
X_TENANT_UUID = "X-Tenant-UUID"

AUTHORIZATION_HEADER = "Authorization"

PARAGRAPH_WITH_BREAK_OR_END_REGEX = r"<p[^>]*>(.*?)(<br>|</p>)"
REMOVE_HTML_TAGS_REGEX = r"<[^>]+>"
ISSUER = "https://noda.com"

TIMEZONE_DEFAULT = "Asia/Tokyo"

UTF8 = "UTF-8"

BYTES_PER_BYTE = 1
BYTES_PER_KILOBYTE = 1024  # 1 KB = 1,024 bytes
BYTES_PER_MEGABYTE = 1024**2  # 1 MB = 1,048,576 bytes
BYTES_PER_GIGABYTE = 1024**3  # 1 GB = 1,073,741,824 bytes
BYTES_PER_TERABYTE = 1024**4  # 1 TB = 1,099,511,627,776 bytes


class StorageRedis(StrEnum):
    STORAGE_CURRENT_USAGE = "noda:storage:usage%s"  # %s is tenant_uuid
    STORAGE_LIMIT = "noda:storage:limit:%s"  # %s is tenant_uuid
